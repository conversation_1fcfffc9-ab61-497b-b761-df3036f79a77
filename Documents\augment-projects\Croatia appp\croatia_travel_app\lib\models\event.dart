import 'package:json_annotation/json_annotation.dart';

part 'event.g.dart';

@JsonSerializable()
class Event {
  final String id;
  final String name;
  final String description;
  final DateTime startDate;
  final DateTime endDate;
  final String location;
  final double? latitude;
  final double? longitude;
  final String region;
  final EventType type;
  final List<String> images;
  final String? website;
  final String? ticketInfo;
  final double? price;
  final bool isFree;
  final List<String> tags;
  final bool isBookmarked;

  Event({
    required this.id,
    required this.name,
    required this.description,
    required this.startDate,
    required this.endDate,
    required this.location,
    this.latitude,
    this.longitude,
    required this.region,
    required this.type,
    this.images = const [],
    this.website,
    this.ticketInfo,
    this.price,
    this.isFree = false,
    this.tags = const [],
    this.isBookmarked = false,
  });

  factory Event.fromJson(Map<String, dynamic> json) => _$EventFromJson(json);
  Map<String, dynamic> toJson() => _$EventToJson(this);

  Event copyWith({
    String? id,
    String? name,
    String? description,
    DateTime? startDate,
    DateTime? endDate,
    String? location,
    double? latitude,
    double? longitude,
    String? region,
    EventType? type,
    List<String>? images,
    String? website,
    String? ticketInfo,
    double? price,
    bool? isFree,
    List<String>? tags,
    bool? isBookmarked,
  }) {
    return Event(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      location: location ?? this.location,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      region: region ?? this.region,
      type: type ?? this.type,
      images: images ?? this.images,
      website: website ?? this.website,
      ticketInfo: ticketInfo ?? this.ticketInfo,
      price: price ?? this.price,
      isFree: isFree ?? this.isFree,
      tags: tags ?? this.tags,
      isBookmarked: isBookmarked ?? this.isBookmarked,
    );
  }
}

enum EventType {
  @JsonValue('festival')
  festival,
  @JsonValue('concert')
  concert,
  @JsonValue('exhibition')
  exhibition,
  @JsonValue('cultural')
  cultural,
  @JsonValue('sports')
  sports,
  @JsonValue('food')
  food,
  @JsonValue('religious')
  religious,
  @JsonValue('traditional')
  traditional,
  @JsonValue('other')
  other,
}
