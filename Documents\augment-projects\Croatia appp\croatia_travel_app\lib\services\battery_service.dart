import 'dart:async';
import 'package:battery_plus/battery_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

class BatteryService {
  static final BatteryService _instance = BatteryService._internal();
  factory BatteryService() => _instance;
  BatteryService._internal();

  final Battery _battery = Battery();
  StreamController<int>? _batteryLevelController;
  Timer? _batteryTimer;
  
  static const String _batterySaverKey = 'battery_saver_enabled';
  static const String _batteryThresholdKey = 'battery_threshold';

  /// Stream pro sledování úrovně baterie
  Stream<int> get batteryLevelStream {
    _batteryLevelController ??= StreamController<int>.broadcast();
    _startBatteryMonitoring();
    return _batteryLevelController!.stream;
  }

  /// Získání aktuální úrovně baterie
  Future<int> getBatteryLevel() async {
    return await _battery.batteryLevel;
  }

  /// Získání stavu baterie (nabíjení/vybíjení)
  Future<BatteryState> getBatteryState() async {
    return await _battery.batteryState;
  }

  /// Kontrola, zda je zapnutý úsporný režim
  Future<bool> isBatterySaverEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_batterySaverKey) ?? false;
  }

  /// Zapnutí/vypnutí úsporného režimu
  Future<void> setBatterySaverMode(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_batterySaverKey, enabled);
  }

  /// Nastavení prahu pro automatické zapnutí úsporného režimu
  Future<void> setBatteryThreshold(int threshold) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_batteryThresholdKey, threshold);
  }

  /// Získání prahu pro úsporný režim
  Future<int> getBatteryThreshold() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_batteryThresholdKey) ?? 20; // Výchozí 20%
  }

  /// Spuštění monitorování baterie
  void _startBatteryMonitoring() {
    if (_batteryTimer != null) return;

    _batteryTimer = Timer.periodic(const Duration(minutes: 1), (timer) async {
      final level = await getBatteryLevel();
      final threshold = await getBatteryThreshold();
      
      _batteryLevelController?.add(level);

      // Automatické zapnutí úsporného režimu
      if (level <= threshold && !await isBatterySaverEnabled()) {
        await setBatterySaverMode(true);
      }
    });
  }

  /// Zastavení monitorování baterie
  void stopBatteryMonitoring() {
    _batteryTimer?.cancel();
    _batteryTimer = null;
    _batteryLevelController?.close();
    _batteryLevelController = null;
  }

  /// Optimalizace pro úsporný režim
  BatteryOptimizationSettings getBatteryOptimizationSettings() {
    return const BatteryOptimizationSettings(
      // Snížení frekvence GPS aktualizací
      gpsUpdateInterval: Duration(minutes: 5),
      
      // Omezení síťových požadavků
      networkRequestsEnabled: false,
      
      // Snížení jasu obrazovky (doporučení)
      recommendedBrightness: 0.3,
      
      // Vypnutí animací
      animationsEnabled: false,
      
      // Omezení synchronizace na pozadí
      backgroundSyncEnabled: false,
      
      // Snížení kvality map
      mapQuality: MapQuality.low,
      
      // Vypnutí automatického stahování obrázků
      autoDownloadImages: false,
    );
  }

  /// Analýza spotřeby energie podle funkcí
  Future<Map<String, double>> analyzePowerConsumption() async {
    // Simulace analýzy spotřeby - v reálné aplikaci by se používaly
    // skutečné metriky z Android/iOS API
    return {
      'GPS': 25.0,
      'Displej': 30.0,
      'Síť': 15.0,
      'Kamera': 10.0,
      'Audio': 8.0,
      'Databáze': 5.0,
      'Ostatní': 7.0,
    };
  }

  /// Doporučení pro úsporu baterie
  Future<List<BatterySavingTip>> getBatterySavingTips() async {
    final level = await getBatteryLevel();
    final state = await getBatteryState();
    
    List<BatterySavingTip> tips = [];

    if (level < 30) {
      tips.add(const BatterySavingTip(
        title: 'Snižte jas obrazovky',
        description: 'Displej spotřebovává nejvíce energie',
        impact: BatteryImpact.high,
      ));
    }

    if (level < 20) {
      tips.add(const BatterySavingTip(
        title: 'Vypněte GPS když není potřeba',
        description: 'GPS výrazně zkracuje výdrž baterie',
        impact: BatteryImpact.high,
      ));
    }

    if (level < 15) {
      tips.add(const BatterySavingTip(
        title: 'Použijte offline režim',
        description: 'Síťové připojení spotřebovává energii',
        impact: BatteryImpact.medium,
      ));
    }

    if (state != BatteryState.charging && level < 10) {
      tips.add(const BatterySavingTip(
        title: 'Urgentně najděte nabíječku',
        description: 'Baterie je kriticky slabá',
        impact: BatteryImpact.critical,
      ));
    }

    return tips;
  }

  void dispose() {
    stopBatteryMonitoring();
  }
}

class BatteryOptimizationSettings {
  final Duration gpsUpdateInterval;
  final bool networkRequestsEnabled;
  final double recommendedBrightness;
  final bool animationsEnabled;
  final bool backgroundSyncEnabled;
  final MapQuality mapQuality;
  final bool autoDownloadImages;

  const BatteryOptimizationSettings({
    required this.gpsUpdateInterval,
    required this.networkRequestsEnabled,
    required this.recommendedBrightness,
    required this.animationsEnabled,
    required this.backgroundSyncEnabled,
    required this.mapQuality,
    required this.autoDownloadImages,
  });
}

enum MapQuality { low, medium, high }

class BatterySavingTip {
  final String title;
  final String description;
  final BatteryImpact impact;

  const BatterySavingTip({
    required this.title,
    required this.description,
    required this.impact,
  });
}

enum BatteryImpact { low, medium, high, critical }
