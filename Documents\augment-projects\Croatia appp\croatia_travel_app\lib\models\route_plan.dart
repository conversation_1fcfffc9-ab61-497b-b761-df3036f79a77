import 'package:json_annotation/json_annotation.dart';

part 'route_plan.g.dart';

@JsonSerializable()
class RoutePlan {
  final String id;
  final String name;
  final String? description;
  final List<RoutePoint> points;
  final DateTime createdDate;
  final DateTime? startDate;
  final int estimatedDuration; // v minutách
  final double estimatedDistance; // v kilometrech
  final RouteType type;
  final String? notes;
  final bool isCompleted;
  final List<String> tags;

  RoutePlan({
    required this.id,
    required this.name,
    this.description,
    this.points = const [],
    required this.createdDate,
    this.startDate,
    this.estimatedDuration = 0,
    this.estimatedDistance = 0.0,
    this.type = RouteType.walking,
    this.notes,
    this.isCompleted = false,
    this.tags = const [],
  });

  factory RoutePlan.fromJson(Map<String, dynamic> json) => _$RoutePlanFromJson(json);
  Map<String, dynamic> toJson() => _$RoutePlanToJson(this);

  RoutePlan copyWith({
    String? id,
    String? name,
    String? description,
    List<RoutePoint>? points,
    DateTime? createdDate,
    DateTime? startDate,
    int? estimatedDuration,
    double? estimatedDistance,
    RouteType? type,
    String? notes,
    bool? isCompleted,
    List<String>? tags,
  }) {
    return RoutePlan(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      points: points ?? this.points,
      createdDate: createdDate ?? this.createdDate,
      startDate: startDate ?? this.startDate,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      estimatedDistance: estimatedDistance ?? this.estimatedDistance,
      type: type ?? this.type,
      notes: notes ?? this.notes,
      isCompleted: isCompleted ?? this.isCompleted,
      tags: tags ?? this.tags,
    );
  }
}

@JsonSerializable()
class RoutePoint {
  final String id;
  final String name;
  final double latitude;
  final double longitude;
  final int order;
  final String? description;
  final int? estimatedStayDuration; // v minutách
  final bool isVisited;
  final DateTime? visitedTime;
  final String? placeId; // Reference na Place

  RoutePoint({
    required this.id,
    required this.name,
    required this.latitude,
    required this.longitude,
    required this.order,
    this.description,
    this.estimatedStayDuration,
    this.isVisited = false,
    this.visitedTime,
    this.placeId,
  });

  factory RoutePoint.fromJson(Map<String, dynamic> json) => _$RoutePointFromJson(json);
  Map<String, dynamic> toJson() => _$RoutePointToJson(this);

  RoutePoint copyWith({
    String? id,
    String? name,
    double? latitude,
    double? longitude,
    int? order,
    String? description,
    int? estimatedStayDuration,
    bool? isVisited,
    DateTime? visitedTime,
    String? placeId,
  }) {
    return RoutePoint(
      id: id ?? this.id,
      name: name ?? this.name,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      order: order ?? this.order,
      description: description ?? this.description,
      estimatedStayDuration: estimatedStayDuration ?? this.estimatedStayDuration,
      isVisited: isVisited ?? this.isVisited,
      visitedTime: visitedTime ?? this.visitedTime,
      placeId: placeId ?? this.placeId,
    );
  }
}

enum RouteType {
  @JsonValue('walking')
  walking,
  @JsonValue('driving')
  driving,
  @JsonValue('cycling')
  cycling,
  @JsonValue('public_transport')
  publicTransport,
  @JsonValue('mixed')
  mixed,
}
