import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/place.dart';
import '../models/event.dart';
import '../services/location_service.dart';
import '../services/route_planning_service.dart';
import '../widgets/map_controls_widget.dart';
import '../widgets/place_info_window.dart';

class MapScreen extends StatefulWidget {
  const MapScreen({super.key});

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  GoogleMapController? _mapController;
  Position? _currentPosition;
  Set<Marker> _markers = {};
  Set<Polyline> _polylines = {};
  List<Place> _places = [];
  List<Event> _events = [];
  String _selectedRegion = 'all';
  bool _showVisitedOnly = false;
  bool _showEvents = true;
  MapType _mapType = MapType.normal;

  final LocationService _locationService = LocationService();
  final RoutePlanningService _routeService = RoutePlanningService();

  // Chorvatské regiony
  final Map<String, LatLng> _regions = {
    'all': const LatLng(45.1, 15.2),
    'istria': const LatLng(45.2, 13.9),
    'dalmatia': const LatLng(43.5, 16.4),
    'slavonia': const LatLng(45.4, 18.7),
    'lika': const LatLng(44.7, 15.6),
    'zagreb': const LatLng(45.8, 15.9),
  };

  @override
  void initState() {
    super.initState();
    _initializeMap();
  }

  Future<void> _initializeMap() async {
    await _requestLocationPermission();
    await _getCurrentLocation();
    await _loadPlaces();
    await _loadEvents();
    _updateMarkers();
  }

  Future<void> _requestLocationPermission() async {
    final status = await Permission.location.request();
    if (status.isDenied) {
      // Zobrazit dialog s vysvětlením
      _showLocationPermissionDialog();
    }
  }

  void _showLocationPermissionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Povolení k poloze'),
        content: const Text(
          'Aplikace potřebuje přístup k vaší poloze pro zobrazení míst v okolí a navigaci.'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Zrušit'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              openAppSettings();
            },
            child: const Text('Nastavení'),
          ),
        ],
      ),
    );
  }

  Future<void> _getCurrentLocation() async {
    try {
      _currentPosition = await _locationService.getCurrentPosition();
      setState(() {});
    } catch (e) {
      print('Chyba při získávání polohy: $e');
    }
  }

  Future<void> _loadPlaces() async {
    // Načtení míst z lokální databáze
    // V reálné aplikaci by se použila databáze
    _places = [
      Place(
        id: '1',
        name: 'Dubrovník - Staré město',
        description: 'Historické centrum Dubrovníku',
        latitude: 42.6407,
        longitude: 18.1077,
        region: 'dalmatia',
        type: PlaceType.monument,
        images: ['dubrovnik1.jpg'],
        isVisited: true,
        visitedDate: DateTime.now().subtract(const Duration(days: 5)),
        rating: 5.0,
        tags: ['UNESCO', 'historie', 'hradby'],
      ),
      Place(
        id: '2',
        name: 'Plitvická jezera',
        description: 'Národní park s kaskádami jezer',
        latitude: 44.8654,
        longitude: 15.5820,
        region: 'lika',
        type: PlaceType.park,
        images: ['plitvice1.jpg'],
        isVisited: false,
        rating: 4.9,
        tags: ['národní park', 'příroda', 'vodopády'],
      ),
      // Další místa...
    ];
  }

  Future<void> _loadEvents() async {
    // Načtení událostí z lokální databáze
    _events = [
      Event(
        id: '1',
        name: 'Dubrovník Summer Festival',
        description: 'Letní kulturní festival',
        startDate: DateTime.now().add(const Duration(days: 30)),
        endDate: DateTime.now().add(const Duration(days: 45)),
        location: 'Dubrovník',
        latitude: 42.6407,
        longitude: 18.1077,
        region: 'dalmatia',
        type: EventType.cultural,
        isFree: false,
        price: 50.0,
      ),
      // Další události...
    ];
  }

  void _updateMarkers() {
    Set<Marker> markers = {};

    // Přidání značek pro místa
    for (final place in _places) {
      if (_selectedRegion != 'all' && place.region != _selectedRegion) continue;
      if (_showVisitedOnly && !place.isVisited) continue;

      markers.add(
        Marker(
          markerId: MarkerId('place_${place.id}'),
          position: LatLng(place.latitude, place.longitude),
          icon: _getPlaceIcon(place),
          infoWindow: InfoWindow(
            title: place.name,
            snippet: place.description,
            onTap: () => _showPlaceDetails(place),
          ),
          onTap: () => _onMarkerTapped(place),
        ),
      );
    }

    // Přidání značek pro události
    if (_showEvents) {
      for (final event in _events) {
        if (_selectedRegion != 'all' && event.region != _selectedRegion) continue;
        if (event.latitude == null || event.longitude == null) continue;

        markers.add(
          Marker(
            markerId: MarkerId('event_${event.id}'),
            position: LatLng(event.latitude!, event.longitude!),
            icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueOrange),
            infoWindow: InfoWindow(
              title: event.name,
              snippet: 'Událost: ${event.startDate.day}.${event.startDate.month}.',
              onTap: () => _showEventDetails(event),
            ),
          ),
        );
      }
    }

    // Přidání značky pro aktuální polohu
    if (_currentPosition != null) {
      markers.add(
        Marker(
          markerId: const MarkerId('current_location'),
          position: LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
          infoWindow: const InfoWindow(title: 'Vaše poloha'),
        ),
      );
    }

    setState(() {
      _markers = markers;
    });
  }

  BitmapDescriptor _getPlaceIcon(Place place) {
    switch (place.type) {
      case PlaceType.monument:
        return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed);
      case PlaceType.beach:
        return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure);
      case PlaceType.restaurant:
        return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueOrange);
      case PlaceType.park:
        return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen);
      default:
        return BitmapDescriptor.defaultMarker;
    }
  }

  void _onMarkerTapped(Place place) {
    // Označení místa jako navštívené při klepnutí
    if (!place.isVisited && _currentPosition != null) {
      final distance = Geolocator.distanceBetween(
        _currentPosition!.latitude,
        _currentPosition!.longitude,
        place.latitude,
        place.longitude,
      );

      // Pokud je uživatel blízko (do 100m), označit jako navštívené
      if (distance <= 100) {
        _markPlaceAsVisited(place);
      }
    }
  }

  void _markPlaceAsVisited(Place place) {
    setState(() {
      final index = _places.indexWhere((p) => p.id == place.id);
      if (index != -1) {
        _places[index] = place.copyWith(
          isVisited: true,
          visitedDate: DateTime.now(),
        );
      }
    });
    _updateMarkers();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Místo "${place.name}" označeno jako navštívené!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showPlaceDetails(Place place) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => PlaceInfoWindow(place: place),
    );
  }

  void _showEventDetails(Event event) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              event.name,
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(event.description),
            const SizedBox(height: 8),
            Text('Datum: ${event.startDate.day}.${event.startDate.month}.${event.startDate.year}'),
            Text('Místo: ${event.location}'),
            if (event.price != null) Text('Cena: ${event.price}€'),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Mapa Chorvatska'),
        actions: [
          PopupMenuButton<String>(
            onSelected: (region) {
              setState(() {
                _selectedRegion = region;
              });
              _updateMarkers();
              _moveToRegion(region);
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'all', child: Text('Celé Chorvatsko')),
              const PopupMenuItem(value: 'istria', child: Text('Istrie')),
              const PopupMenuItem(value: 'dalmatia', child: Text('Dalmácie')),
              const PopupMenuItem(value: 'slavonia', child: Text('Slavonie')),
              const PopupMenuItem(value: 'lika', child: Text('Lika')),
              const PopupMenuItem(value: 'zagreb', child: Text('Zagreb')),
            ],
          ),
        ],
      ),
      body: Stack(
        children: [
          GoogleMap(
            onMapCreated: (controller) => _mapController = controller,
            initialCameraPosition: CameraPosition(
              target: _regions['all']!,
              zoom: 7.0,
            ),
            markers: _markers,
            polylines: _polylines,
            mapType: _mapType,
            myLocationEnabled: true,
            myLocationButtonEnabled: false,
            zoomControlsEnabled: false,
          ),
          
          // Ovládací prvky mapy
          Positioned(
            top: 16,
            right: 16,
            child: MapControlsWidget(
              onMapTypeChanged: (type) {
                setState(() {
                  _mapType = type;
                });
              },
              onShowVisitedToggled: (show) {
                setState(() {
                  _showVisitedOnly = show;
                });
                _updateMarkers();
              },
              onShowEventsToggled: (show) {
                setState(() {
                  _showEvents = show;
                });
                _updateMarkers();
              },
              showVisitedOnly: _showVisitedOnly,
              showEvents: _showEvents,
              mapType: _mapType,
            ),
          ),
        ],
      ),
      floatingActionButton: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          FloatingActionButton(
            heroTag: 'location',
            onPressed: _goToCurrentLocation,
            child: const Icon(Icons.my_location),
          ),
          const SizedBox(height: 8),
          FloatingActionButton(
            heroTag: 'route',
            onPressed: _showRoutePlanning,
            child: const Icon(Icons.directions),
          ),
        ],
      ),
    );
  }

  void _moveToRegion(String region) {
    if (_mapController != null && _regions.containsKey(region)) {
      _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(_regions[region]!, region == 'all' ? 7.0 : 9.0),
      );
    }
  }

  void _goToCurrentLocation() {
    if (_currentPosition != null && _mapController != null) {
      _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(
          LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
          15.0,
        ),
      );
    }
  }

  void _showRoutePlanning() {
    // Implementace plánování tras
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: const Column(
          children: [
            Text(
              'Plánování trasy',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            // Implementace UI pro plánování tras
          ],
        ),
      ),
    );
  }
}
