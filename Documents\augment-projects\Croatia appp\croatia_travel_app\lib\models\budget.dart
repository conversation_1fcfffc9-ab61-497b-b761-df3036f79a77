import 'package:json_annotation/json_annotation.dart';

part 'budget.g.dart';

@JsonSerializable()
class BudgetEntry {
  final String id;
  final String title;
  final String? description;
  final double amount;
  final String currency;
  final BudgetCategory category;
  final DateTime date;
  final String? location;
  final String? receipt; // Cesta k obrázku účtenky
  final List<String> tags;

  BudgetEntry({
    required this.id,
    required this.title,
    this.description,
    required this.amount,
    required this.currency,
    required this.category,
    required this.date,
    this.location,
    this.receipt,
    this.tags = const [],
  });

  factory BudgetEntry.fromJson(Map<String, dynamic> json) => _$BudgetEntryFromJson(json);
  Map<String, dynamic> toJson() => _$BudgetEntryToJson(this);

  BudgetEntry copyWith({
    String? id,
    String? title,
    String? description,
    double? amount,
    String? currency,
    BudgetCategory? category,
    DateTime? date,
    String? location,
    String? receipt,
    List<String>? tags,
  }) {
    return BudgetEntry(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      category: category ?? this.category,
      date: date ?? this.date,
      location: location ?? this.location,
      receipt: receipt ?? this.receipt,
      tags: tags ?? this.tags,
    );
  }
}

@JsonSerializable()
class Budget {
  final String id;
  final String name;
  final double totalBudget;
  final String currency;
  final DateTime startDate;
  final DateTime endDate;
  final List<BudgetEntry> entries;
  final Map<BudgetCategory, double> categoryLimits;

  Budget({
    required this.id,
    required this.name,
    required this.totalBudget,
    required this.currency,
    required this.startDate,
    required this.endDate,
    this.entries = const [],
    this.categoryLimits = const {},
  });

  factory Budget.fromJson(Map<String, dynamic> json) => _$BudgetFromJson(json);
  Map<String, dynamic> toJson() => _$BudgetToJson(this);

  double get totalSpent => entries.fold(0.0, (sum, entry) => sum + entry.amount);
  double get remainingBudget => totalBudget - totalSpent;
  
  Map<BudgetCategory, double> get spentByCategory {
    final Map<BudgetCategory, double> spent = {};
    for (final entry in entries) {
      spent[entry.category] = (spent[entry.category] ?? 0.0) + entry.amount;
    }
    return spent;
  }

  Budget copyWith({
    String? id,
    String? name,
    double? totalBudget,
    String? currency,
    DateTime? startDate,
    DateTime? endDate,
    List<BudgetEntry>? entries,
    Map<BudgetCategory, double>? categoryLimits,
  }) {
    return Budget(
      id: id ?? this.id,
      name: name ?? this.name,
      totalBudget: totalBudget ?? this.totalBudget,
      currency: currency ?? this.currency,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      entries: entries ?? this.entries,
      categoryLimits: categoryLimits ?? this.categoryLimits,
    );
  }
}

enum BudgetCategory {
  @JsonValue('accommodation')
  accommodation,
  @JsonValue('food')
  food,
  @JsonValue('transport')
  transport,
  @JsonValue('entertainment')
  entertainment,
  @JsonValue('shopping')
  shopping,
  @JsonValue('activities')
  activities,
  @JsonValue('emergency')
  emergency,
  @JsonValue('other')
  other,
}
