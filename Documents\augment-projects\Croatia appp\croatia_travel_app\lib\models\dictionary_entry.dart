import 'package:json_annotation/json_annotation.dart';

part 'dictionary_entry.g.dart';

@JsonSerializable()
class DictionaryEntry {
  final String id;
  final String croatian;
  final String english;
  final String? czech;
  final String? pronunciation;
  final String? audioPath;
  final DictionaryCategory category;
  final String? context;
  final List<String> examples;
  final String region;
  final bool isCommon;
  final List<String> tags;

  DictionaryEntry({
    required this.id,
    required this.croatian,
    required this.english,
    this.czech,
    this.pronunciation,
    this.audioPath,
    required this.category,
    this.context,
    this.examples = const [],
    this.region = 'general',
    this.isCommon = false,
    this.tags = const [],
  });

  factory DictionaryEntry.fromJson(Map<String, dynamic> json) => _$DictionaryEntryFromJson(json);
  Map<String, dynamic> toJson() => _$DictionaryEntryToJson(this);
}

@JsonSerializable()
class Phrase {
  final String id;
  final String croatian;
  final String english;
  final String? czech;
  final String? pronunciation;
  final String? audioPath;
  final PhraseCategory category;
  final String? situation;
  final bool isEssential;
  final List<String> variations;

  Phrase({
    required this.id,
    required this.croatian,
    required this.english,
    this.czech,
    this.pronunciation,
    this.audioPath,
    required this.category,
    this.situation,
    this.isEssential = false,
    this.variations = const [],
  });

  factory Phrase.fromJson(Map<String, dynamic> json) => _$PhraseFromJson(json);
  Map<String, dynamic> toJson() => _$PhraseToJson(this);
}

enum DictionaryCategory {
  @JsonValue('basic')
  basic,
  @JsonValue('food')
  food,
  @JsonValue('transport')
  transport,
  @JsonValue('accommodation')
  accommodation,
  @JsonValue('emergency')
  emergency,
  @JsonValue('shopping')
  shopping,
  @JsonValue('directions')
  directions,
  @JsonValue('time')
  time,
  @JsonValue('numbers')
  numbers,
  @JsonValue('weather')
  weather,
  @JsonValue('culture')
  culture,
  @JsonValue('nature')
  nature,
}

enum PhraseCategory {
  @JsonValue('greetings')
  greetings,
  @JsonValue('polite')
  polite,
  @JsonValue('questions')
  questions,
  @JsonValue('restaurant')
  restaurant,
  @JsonValue('hotel')
  hotel,
  @JsonValue('shopping')
  shopping,
  @JsonValue('emergency')
  emergency,
  @JsonValue('directions')
  directions,
  @JsonValue('transport')
  transport,
  @JsonValue('small_talk')
  smallTalk,
}
