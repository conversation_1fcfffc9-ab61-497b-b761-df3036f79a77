import 'package:json_annotation/json_annotation.dart';

part 'diary_entry.g.dart';

@JsonSerializable()
class DiaryEntry {
  final String id;
  final String title;
  final String content;
  final DateTime date;
  final String? location;
  final double? latitude;
  final double? longitude;
  final List<String> photos;
  final List<String> audioRecordings;
  final String? weather;
  final double? temperature;
  final List<String> tags;
  final EntryMood? mood;
  final double? rating;
  final List<String> visitedPlaces; // IDs navštívených míst

  DiaryEntry({
    required this.id,
    required this.title,
    required this.content,
    required this.date,
    this.location,
    this.latitude,
    this.longitude,
    this.photos = const [],
    this.audioRecordings = const [],
    this.weather,
    this.temperature,
    this.tags = const [],
    this.mood,
    this.rating,
    this.visitedPlaces = const [],
  });

  factory DiaryEntry.fromJson(Map<String, dynamic> json) => _$DiaryEntryFromJson(json);
  Map<String, dynamic> toJson() => _$DiaryEntryToJson(this);

  DiaryEntry copyWith({
    String? id,
    String? title,
    String? content,
    DateTime? date,
    String? location,
    double? latitude,
    double? longitude,
    List<String>? photos,
    List<String>? audioRecordings,
    String? weather,
    double? temperature,
    List<String>? tags,
    EntryMood? mood,
    double? rating,
    List<String>? visitedPlaces,
  }) {
    return DiaryEntry(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      date: date ?? this.date,
      location: location ?? this.location,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      photos: photos ?? this.photos,
      audioRecordings: audioRecordings ?? this.audioRecordings,
      weather: weather ?? this.weather,
      temperature: temperature ?? this.temperature,
      tags: tags ?? this.tags,
      mood: mood ?? this.mood,
      rating: rating ?? this.rating,
      visitedPlaces: visitedPlaces ?? this.visitedPlaces,
    );
  }
}

enum EntryMood {
  @JsonValue('excellent')
  excellent,
  @JsonValue('good')
  good,
  @JsonValue('neutral')
  neutral,
  @JsonValue('bad')
  bad,
  @JsonValue('terrible')
  terrible,
}
