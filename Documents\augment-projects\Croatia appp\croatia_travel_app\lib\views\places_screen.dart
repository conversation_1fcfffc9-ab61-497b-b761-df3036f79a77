import 'package:flutter/material.dart';
import '../models/place.dart';
import '../services/location_service.dart';
import '../widgets/place_card.dart';
import '../widgets/filter_widget.dart';

class PlacesScreen extends StatefulWidget {
  const PlacesScreen({super.key});

  @override
  State<PlacesScreen> createState() => _PlacesScreenState();
}

class _PlacesScreenState extends State<PlacesScreen> {
  List<Place> _allPlaces = [];
  List<Place> _filteredPlaces = [];
  String _searchQuery = '';
  String _selectedRegion = 'all';
  PlaceType? _selectedType;
  bool _showVisitedOnly = false;
  bool _sortByDistance = false;
  
  final LocationService _locationService = LocationService();
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadPlaces();
  }

  Future<void> _loadPlaces() async {
    // Načtení míst z databáze - zde simulace dat
    _allPlaces = [
      Place(
        id: '1',
        name: '<PERSON>rovník - <PERSON><PERSON> město',
        description: 'Historické centrum Dubrovníku s krásnými hradbami a úzkými uličkami. UNESCO světové dědictví.',
        latitude: 42.6407,
        longitude: 18.1077,
        region: 'dalmatia',
        type: PlaceType.monument,
        images: ['dubrovnik1.jpg', 'dubrovnik2.jpg'],
        isVisited: true,
        visitedDate: DateTime.now().subtract(const Duration(days: 5)),
        rating: 5.0,
        tags: ['UNESCO', 'historie', 'hradby', 'Game of Thrones'],
        additionalInfo: {
          'opening_hours': '24/7',
          'entrance_fee': '30 EUR',
          'best_time': 'Ráno nebo večer',
        },
      ),
      Place(
        id: '2',
        name: 'Plitvická jezera',
        description: 'Národní park s 16 terasovitými jezery spojenými vodopády.',
        latitude: 44.8654,
        longitude: 15.5820,
        region: 'lika',
        type: PlaceType.park,
        images: ['plitvice1.jpg', 'plitvice2.jpg'],
        isVisited: false,
        rating: 4.9,
        tags: ['národní park', 'příroda', 'vodopády', 'UNESCO'],
        additionalInfo: {
          'opening_hours': '7:00-19:00',
          'entrance_fee': '40 EUR',
          'duration': '4-8 hodin',
        },
      ),
      Place(
        id: '3',
        name: 'Zlatni Rat - Bol',
        description: 'Nejslavnější pláž v Chorvatsku na ostrově Brač.',
        latitude: 43.2567,
        longitude: 16.6378,
        region: 'dalmatia',
        type: PlaceType.beach,
        images: ['zlatni_rat1.jpg'],
        isVisited: false,
        rating: 4.7,
        tags: ['pláž', 'windsurfing', 'ostrov Brač'],
        additionalInfo: {
          'best_time': 'Červen-září',
          'activities': 'Windsurfing, kitesurfing',
        },
      ),
      Place(
        id: '4',
        name: 'Rovinj',
        description: 'Romantické město v Istrii s benátskou architekturou.',
        latitude: 45.0811,
        longitude: 13.6387,
        region: 'istria',
        type: PlaceType.monument,
        images: ['rovinj1.jpg'],
        isVisited: true,
        visitedDate: DateTime.now().subtract(const Duration(days: 15)),
        rating: 4.6,
        tags: ['Istrie', 'romantika', 'benátská architektura'],
      ),
      Place(
        id: '5',
        name: 'Krka National Park',
        description: 'Národní park se sedmi vodopády a možností koupání.',
        latitude: 43.8069,
        longitude: 15.9614,
        region: 'dalmatia',
        type: PlaceType.park,
        images: ['krka1.jpg'],
        isVisited: false,
        rating: 4.5,
        tags: ['národní park', 'vodopády', 'koupání'],
      ),
    ];

    _filteredPlaces = List.from(_allPlaces);
    setState(() {});
  }

  void _filterPlaces() {
    List<Place> filtered = List.from(_allPlaces);

    // Filtrování podle vyhledávání
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((place) {
        return place.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               place.description.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               place.tags.any((tag) => tag.toLowerCase().contains(_searchQuery.toLowerCase()));
      }).toList();
    }

    // Filtrování podle regionu
    if (_selectedRegion != 'all') {
      filtered = filtered.where((place) => place.region == _selectedRegion).toList();
    }

    // Filtrování podle typu
    if (_selectedType != null) {
      filtered = filtered.where((place) => place.type == _selectedType).toList();
    }

    // Filtrování navštívených míst
    if (_showVisitedOnly) {
      filtered = filtered.where((place) => place.isVisited).toList();
    }

    // Řazení podle vzdálenosti
    if (_sortByDistance) {
      _sortPlacesByDistance(filtered);
    } else {
      // Řazení podle hodnocení
      filtered.sort((a, b) => (b.rating ?? 0).compareTo(a.rating ?? 0));
    }

    setState(() {
      _filteredPlaces = filtered;
    });
  }

  Future<void> _sortPlacesByDistance(List<Place> places) async {
    try {
      final currentPosition = await _locationService.getCurrentPosition();
      
      places.sort((a, b) {
        final distanceA = _locationService.calculateDistance(
          currentPosition.latitude,
          currentPosition.longitude,
          a.latitude,
          a.longitude,
        );
        final distanceB = _locationService.calculateDistance(
          currentPosition.latitude,
          currentPosition.longitude,
          b.latitude,
          b.longitude,
        );
        return distanceA.compareTo(distanceB);
      });
    } catch (e) {
      print('Chyba při řazení podle vzdálenosti: $e');
    }
  }

  void _showFilterDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => FilterWidget(
        selectedRegion: _selectedRegion,
        selectedType: _selectedType,
        showVisitedOnly: _showVisitedOnly,
        sortByDistance: _sortByDistance,
        onRegionChanged: (region) {
          setState(() {
            _selectedRegion = region;
          });
          _filterPlaces();
        },
        onTypeChanged: (type) {
          setState(() {
            _selectedType = type;
          });
          _filterPlaces();
        },
        onVisitedOnlyChanged: (value) {
          setState(() {
            _showVisitedOnly = value;
          });
          _filterPlaces();
        },
        onSortByDistanceChanged: (value) {
          setState(() {
            _sortByDistance = value;
          });
          _filterPlaces();
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Místa k návštěvě'),
        actions: [
          IconButton(
            onPressed: _showFilterDialog,
            icon: const Icon(Icons.filter_list),
          ),
        ],
      ),
      body: Column(
        children: [
          // Vyhledávací pole
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Vyhledat místa...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                          _filterPlaces();
                        },
                        icon: const Icon(Icons.clear),
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
                _filterPlaces();
              },
            ),
          ),

          // Statistiky
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Text(
                  'Nalezeno: ${_filteredPlaces.length} míst',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const Spacer(),
                Text(
                  'Navštíveno: ${_allPlaces.where((p) => p.isVisited).length}',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          ),

          const SizedBox(height: 8),

          // Seznam míst
          Expanded(
            child: _filteredPlaces.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.location_off,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Žádná místa nenalezena',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Zkuste změnit filtry nebo vyhledávání',
                          style: TextStyle(
                            color: Colors.grey[500],
                          ),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _filteredPlaces.length,
                    itemBuilder: (context, index) {
                      final place = _filteredPlaces[index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 12),
                        child: PlaceCard(
                          place: place,
                          onTap: () => _navigateToPlaceDetail(place),
                          onVisitedToggle: () => _toggleVisited(place),
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  void _navigateToPlaceDetail(Place place) {
    // Navigace na detail místa
    Navigator.pushNamed(context, '/places/${place.id}');
  }

  void _toggleVisited(Place place) {
    setState(() {
      final index = _allPlaces.indexWhere((p) => p.id == place.id);
      if (index != -1) {
        _allPlaces[index] = place.copyWith(
          isVisited: !place.isVisited,
          visitedDate: !place.isVisited ? DateTime.now() : null,
        );
      }
    });
    _filterPlaces();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
