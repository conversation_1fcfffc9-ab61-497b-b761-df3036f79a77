import 'package:flutter/material.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../services/sync_service.dart';
import '../services/battery_service.dart';
import '../widgets/weather_widget.dart';
import '../widgets/quick_actions_widget.dart';
import '../widgets/recommendations_widget.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  bool _isOnline = false;
  bool _isSyncing = false;
  int _batteryLevel = 100;
  bool _batterySaverMode = false;

  @override
  void initState() {
    super.initState();
    _checkConnectivity();
    _initializeBatteryMonitoring();
  }

  Future<void> _checkConnectivity() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    setState(() {
      _isOnline = connectivityResult != ConnectivityResult.none;
    });

    if (_isOnline) {
      _syncData();
    }

    // Poslouchání změn připojení
    Connectivity().onConnectivityChanged.listen((ConnectivityResult result) {
      final wasOffline = !_isOnline;
      setState(() {
        _isOnline = result != ConnectivityResult.none;
      });

      // Automatická synchronizace po připojení k internetu
      if (wasOffline && _isOnline) {
        _syncData();
      }
    });
  }

  Future<void> _initializeBatteryMonitoring() async {
    final batteryService = BatteryService();
    final level = await batteryService.getBatteryLevel();
    setState(() {
      _batteryLevel = level;
      _batterySaverMode = level < 20; // Automatický úsporný režim pod 20%
    });

    // Monitorování baterie
    batteryService.batteryLevelStream.listen((level) {
      setState(() {
        _batteryLevel = level;
        if (level < 20 && !_batterySaverMode) {
          _batterySaverMode = true;
          _showBatterySaverDialog();
        }
      });
    });
  }

  Future<void> _syncData() async {
    if (_isSyncing) return;

    setState(() {
      _isSyncing = true;
    });

    try {
      final syncService = SyncService();
      await syncService.syncAllData();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Data byla úspěšně synchronizována'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Chyba při synchronizaci: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSyncing = false;
        });
      }
    }
  }

  void _showBatterySaverDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Úsporný režim'),
        content: const Text(
          'Baterie je slabá. Chcete zapnout úsporný režim pro prodloužení výdrže?'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Ne'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _batterySaverMode = true;
              });
              Navigator.pop(context);
            },
            child: const Text('Ano'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Croatia Travel'),
        actions: [
          // Indikátor připojení
          Icon(
            _isOnline ? Icons.wifi : Icons.wifi_off,
            color: _isOnline ? Colors.green : Colors.red,
          ),
          const SizedBox(width: 8),
          
          // Indikátor baterie
          Row(
            children: [
              Icon(
                _batterySaverMode ? Icons.battery_saver : Icons.battery_std,
                color: _batteryLevel < 20 ? Colors.red : Colors.green,
              ),
              Text('$_batteryLevel%'),
            ],
          ),
          const SizedBox(width: 8),
          
          // Tlačítko synchronizace
          IconButton(
            onPressed: _isOnline && !_isSyncing ? _syncData : null,
            icon: _isSyncing 
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.sync),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _syncData,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Stav připojení a synchronizace
              if (!_isOnline)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange.shade100,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.orange),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.wifi_off, color: Colors.orange),
                      SizedBox(width: 8),
                      Text(
                        'Offline režim - některé funkce jsou omezené',
                        style: TextStyle(color: Colors.orange),
                      ),
                    ],
                  ),
                ),
              
              if (!_isOnline) const SizedBox(height: 16),

              // Úsporný režim upozornění
              if (_batterySaverMode)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.amber.shade100,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.amber),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.battery_saver, color: Colors.amber),
                      const SizedBox(width: 8),
                      const Expanded(
                        child: Text(
                          'Úsporný režim je aktivní',
                          style: TextStyle(color: Colors.amber),
                        ),
                      ),
                      TextButton(
                        onPressed: () {
                          setState(() {
                            _batterySaverMode = false;
                          });
                        },
                        child: const Text('Vypnout'),
                      ),
                    ],
                  ),
                ),

              if (_batterySaverMode) const SizedBox(height: 16),

              // Počasí widget
              const WeatherWidget(),
              const SizedBox(height: 24),

              // Rychlé akce
              const Text(
                'Rychlé akce',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),
              const QuickActionsWidget(),
              const SizedBox(height: 24),

              // Personalizovaná doporučení
              const Text(
                'Doporučení pro vás',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),
              RecommendationsWidget(batterySaverMode: _batterySaverMode),
            ],
          ),
        ),
      ),
    );
  }
}
