import 'package:json_annotation/json_annotation.dart';

part 'cuisine.g.dart';

@JsonSerializable()
class CuisineItem {
  final String id;
  final String name;
  final String description;
  final String region;
  final CuisineType type;
  final List<String> ingredients;
  final String? recipe;
  final List<String> images;
  final List<String> restaurants; // IDs restaurací kde se podává
  final bool isVegetarian;
  final bool isVegan;
  final bool isGlutenFree;
  final List<String> allergens;
  final double? averagePrice;
  final double? rating;
  final List<String> tags;

  CuisineItem({
    required this.id,
    required this.name,
    required this.description,
    required this.region,
    required this.type,
    this.ingredients = const [],
    this.recipe,
    this.images = const [],
    this.restaurants = const [],
    this.isVegetarian = false,
    this.isVegan = false,
    this.isGlutenFree = false,
    this.allergens = const [],
    this.averagePrice,
    this.rating,
    this.tags = const [],
  });

  factory CuisineItem.fromJson(Map<String, dynamic> json) => _$CuisineItemFromJson(json);
  Map<String, dynamic> toJson() => _$CuisineItemToJson(this);

  CuisineItem copyWith({
    String? id,
    String? name,
    String? description,
    String? region,
    CuisineType? type,
    List<String>? ingredients,
    String? recipe,
    List<String>? images,
    List<String>? restaurants,
    bool? isVegetarian,
    bool? isVegan,
    bool? isGlutenFree,
    List<String>? allergens,
    double? averagePrice,
    double? rating,
    List<String>? tags,
  }) {
    return CuisineItem(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      region: region ?? this.region,
      type: type ?? this.type,
      ingredients: ingredients ?? this.ingredients,
      recipe: recipe ?? this.recipe,
      images: images ?? this.images,
      restaurants: restaurants ?? this.restaurants,
      isVegetarian: isVegetarian ?? this.isVegetarian,
      isVegan: isVegan ?? this.isVegan,
      isGlutenFree: isGlutenFree ?? this.isGlutenFree,
      allergens: allergens ?? this.allergens,
      averagePrice: averagePrice ?? this.averagePrice,
      rating: rating ?? this.rating,
      tags: tags ?? this.tags,
    );
  }
}

enum CuisineType {
  @JsonValue('main_dish')
  mainDish,
  @JsonValue('appetizer')
  appetizer,
  @JsonValue('dessert')
  dessert,
  @JsonValue('drink')
  drink,
  @JsonValue('snack')
  snack,
  @JsonValue('soup')
  soup,
  @JsonValue('seafood')
  seafood,
  @JsonValue('meat')
  meat,
  @JsonValue('pasta')
  pasta,
  @JsonValue('bread')
  bread,
}
