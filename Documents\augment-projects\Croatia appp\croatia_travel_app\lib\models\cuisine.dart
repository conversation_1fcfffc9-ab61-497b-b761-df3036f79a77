import 'package:json_annotation/json_annotation.dart';

// part 'cuisine.g.dart'; // Dočasně <PERSON>

@JsonSerializable()
class CuisineItem {
  final String id;
  final String name;
  final String description;
  final String region;
  final CuisineType type;
  final List<String> ingredients;
  final String? recipe;
  final List<String> images;
  final List<String> restaurants; // IDs restaurací kde se podává
  final bool isVegetarian;
  final bool isVegan;
  final bool isGlutenFree;
  final List<String> allergens;
  final double? averagePrice;
  final double? rating;
  final List<String> tags;

  CuisineItem({
    required this.id,
    required this.name,
    required this.description,
    required this.region,
    required this.type,
    this.ingredients = const [],
    this.recipe,
    this.images = const [],
    this.restaurants = const [],
    this.isVegetarian = false,
    this.isVegan = false,
    this.isGlutenFree = false,
    this.allergens = const [],
    this.averagePrice,
    this.rating,
    this.tags = const [],
  });

  factory CuisineItem.fromJson(Map<String, dynamic> json) {
    return CuisineItem(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      region: json['region'],
      type: CuisineType.values.firstWhere((e) => e.name == json['type']),
      ingredients: List<String>.from(json['ingredients'] ?? []),
      recipe: json['recipe'],
      images: List<String>.from(json['images'] ?? []),
      restaurants: List<String>.from(json['restaurants'] ?? []),
      isVegetarian: json['isVegetarian'] ?? false,
      isVegan: json['isVegan'] ?? false,
      isGlutenFree: json['isGlutenFree'] ?? false,
      allergens: List<String>.from(json['allergens'] ?? []),
      averagePrice: json['averagePrice'],
      rating: json['rating'],
      tags: List<String>.from(json['tags'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'region': region,
      'type': type.name,
      'ingredients': ingredients,
      'recipe': recipe,
      'images': images,
      'restaurants': restaurants,
      'isVegetarian': isVegetarian,
      'isVegan': isVegan,
      'isGlutenFree': isGlutenFree,
      'allergens': allergens,
      'averagePrice': averagePrice,
      'rating': rating,
      'tags': tags,
    };
  }

  CuisineItem copyWith({
    String? id,
    String? name,
    String? description,
    String? region,
    CuisineType? type,
    List<String>? ingredients,
    String? recipe,
    List<String>? images,
    List<String>? restaurants,
    bool? isVegetarian,
    bool? isVegan,
    bool? isGlutenFree,
    List<String>? allergens,
    double? averagePrice,
    double? rating,
    List<String>? tags,
  }) {
    return CuisineItem(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      region: region ?? this.region,
      type: type ?? this.type,
      ingredients: ingredients ?? this.ingredients,
      recipe: recipe ?? this.recipe,
      images: images ?? this.images,
      restaurants: restaurants ?? this.restaurants,
      isVegetarian: isVegetarian ?? this.isVegetarian,
      isVegan: isVegan ?? this.isVegan,
      isGlutenFree: isGlutenFree ?? this.isGlutenFree,
      allergens: allergens ?? this.allergens,
      averagePrice: averagePrice ?? this.averagePrice,
      rating: rating ?? this.rating,
      tags: tags ?? this.tags,
    );
  }
}

enum CuisineType {
  @JsonValue('main_dish')
  mainDish,
  @JsonValue('appetizer')
  appetizer,
  @JsonValue('dessert')
  dessert,
  @JsonValue('drink')
  drink,
  @JsonValue('snack')
  snack,
  @JsonValue('soup')
  soup,
  @JsonValue('seafood')
  seafood,
  @JsonValue('meat')
  meat,
  @JsonValue('pasta')
  pasta,
  @JsonValue('bread')
  bread,
}
